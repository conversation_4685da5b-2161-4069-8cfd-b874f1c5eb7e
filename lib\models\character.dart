import 'package:uuid/uuid.dart';
import 'attributes.dart';
import 'race.dart';
import 'character_class.dart';

/// 角色数据模型
/// 基于Microlite20规则的完整角色数据结构
class Character {
  final String id;
  String name;
  String gender;
  int age;
  Race race;
  CharacterClass characterClass;
  int level;
  int experience;
  Attributes attributes;
  int currency; // 铜币
  
  // 构造函数
  Character({
    String? id,
    required this.name,
    required this.gender,
    required this.age,
    required this.race,
    required this.characterClass,
    this.level = 1,
    this.experience = 0,
    required this.attributes,
    int? currency,
  }) : id = id ?? const Uuid().v4(),
       currency = currency ?? _getStartingCurrency(characterClass);

  /// 获取起始金币（转换为铜币）
  static int _getStartingCurrency(CharacterClass characterClass) {
    switch (characterClass.name) {
      case 'Fighter':
        return 150 * 100; // 150gp = 15000cp
      case 'Rogue':
        return 125 * 100; // 125gp = 12500cp
      case 'Mage':
        return 75 * 100;  // 75gp = 7500cp
      case 'Cleric':
        return 120 * 100; // 120gp = 12000cp
      default:
        return 100 * 100; // 默认100gp
    }
  }

  /// 计算属性加成
  int getAttributeModifier(int attributeValue) {
    return ((attributeValue - 10) / 2).floor();
  }

  /// 获取力量加成
  int get strengthModifier => getAttributeModifier(attributes.strength);

  /// 获取敏捷加成
  int get dexterityModifier => getAttributeModifier(attributes.dexterity);

  /// 获取心智加成
  int get mindModifier => getAttributeModifier(attributes.mind);

  /// 计算最大生命值
  int get maxHitPoints {
    // 基础HP = 属性值 + 等级奖励HP
    return attributes.strength + attributes.hpBonus;
  }

  /// 计算护甲等级
  int get armorClass {
    // AC = 10 + DEX加成 + 护甲加成 + 其他加成
    int baseAC = 10;
    int dexBonus = dexterityModifier;
    int armorBonus = 0; // 暂时没有装备系统
    
    return baseAC + dexBonus + armorBonus;
  }

  /// 计算攻击加成
  int get attackBonus {
    // 攻击加成 = 属性加成 + 等级
    return strengthModifier + level;
  }

  /// 获取技能等级
  int getSkillLevel(String skillName) {
    int baseLevel = 0;
    int classBonus = 0;
    int raceBonus = 0;

    // 职业加成
    if (characterClass.specialSkill == skillName) {
      classBonus = 3;
    }

    // 种族加成
    if (race.name == 'Human') {
      raceBonus = 1; // 人类所有技能+1
    }

    return baseLevel + classBonus + raceBonus;
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'gender': gender,
      'age': age,
      'race': race.toJson(),
      'characterClass': characterClass.toJson(),
      'level': level,
      'experience': experience,
      'attributes': attributes.toJson(),
      'currency': currency,
    };
  }

  /// 从JSON创建角色
  factory Character.fromJson(Map<String, dynamic> json) {
    return Character(
      id: json['id'],
      name: json['name'],
      gender: json['gender'],
      age: json['age'],
      race: Race.fromJson(json['race']),
      characterClass: CharacterClass.fromJson(json['characterClass']),
      level: json['level'],
      experience: json['experience'],
      attributes: Attributes.fromJson(json['attributes']),
      currency: json['currency'],
    );
  }

  /// 创建角色副本
  Character copyWith({
    String? name,
    String? gender,
    int? age,
    Race? race,
    CharacterClass? characterClass,
    int? level,
    int? experience,
    Attributes? attributes,
    int? currency,
  }) {
    return Character(
      id: id,
      name: name ?? this.name,
      gender: gender ?? this.gender,
      age: age ?? this.age,
      race: race ?? this.race,
      characterClass: characterClass ?? this.characterClass,
      level: level ?? this.level,
      experience: experience ?? this.experience,
      attributes: attributes ?? this.attributes,
      currency: currency ?? this.currency,
    );
  }

  @override
  String toString() {
    return 'Character(id: $id, name: $name, race: ${race.name}, class: ${characterClass.name}, level: $level)';
  }
}
