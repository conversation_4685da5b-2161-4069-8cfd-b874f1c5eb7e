/// 职业数据模型
/// 基于Microlite20规则的职业系统
class CharacterClass {
  final String name;
  final String displayName;
  final String description;
  final String armorRestriction;
  final String specialSkill;
  final int startingGold;
  final String characteristics;

  const CharacterClass({
    required this.name,
    required this.displayName,
    required this.description,
    required this.armorRestriction,
    required this.specialSkill,
    required this.startingGold,
    required this.characteristics,
  });

  /// 预定义的职业列表
  static const List<CharacterClass> allClasses = [
    CharacterClass(
      name: 'Fighter',
      displayName: '战士',
      description: '前排肉盾，擅长近战和防御',
      armorRestriction: '可穿任何护甲和使用盾牌',
      specialSkill: 'Physical',
      startingGold: 150,
      characteristics: '前排肉盾，高生存能力',
    ),
    CharacterClass(
      name: 'Rogue',
      displayName: '盗贼',
      description: '敏捷的潜行者，擅长偷袭和技巧',
      armorRestriction: '只能穿轻甲',
      specialSkill: 'Subterfuge',
      startingGold: 125,
      characteristics: '高机动性，擅长偷袭',
    ),
    CharacterClass(
      name: 'Mage',
      displayName: '法师',
      description: '强大的施法者，掌握奥术魔法',
      armorRestriction: '不能穿护甲',
      specialSkill: 'Knowledge',
      startingGold: 75,
      characteristics: '强大法术输出，但防御脆弱',
    ),
    CharacterClass(
      name: 'Cleric',
      displayName: '牧师',
      description: '神圣的治疗者，掌握神术魔法',
      armorRestriction: '可穿轻甲和中甲',
      specialSkill: 'Communication',
      startingGold: 120,
      characteristics: '治疗支援，多用途',
    ),
  ];

  /// 根据名称获取职业
  static CharacterClass? getClassByName(String name) {
    try {
      return allClasses.firstWhere((characterClass) => characterClass.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 获取专精技能加成
  int getSkillBonus(String skillName) {
    return skillName == specialSkill ? 3 : 0;
  }

  /// 获取职业图标
  String get icon {
    switch (name) {
      case 'Fighter':
        return '⚔️';
      case 'Rogue':
        return '🗡️';
      case 'Mage':
        return '🔮';
      case 'Cleric':
        return '✨';
      default:
        return '❓';
    }
  }

  /// 获取职业颜色
  String get colorHex {
    switch (name) {
      case 'Fighter':
        return '#8B4513'; // 棕色
      case 'Rogue':
        return '#2F4F4F'; // 暗灰色
      case 'Mage':
        return '#4169E1'; // 蓝色
      case 'Cleric':
        return '#FFD700'; // 金色
      default:
        return '#808080'; // 灰色
    }
  }

  /// 检查是否可以穿某种护甲
  bool canWearArmor(String armorType) {
    switch (name) {
      case 'Fighter':
        return true; // 可穿任何护甲
      case 'Rogue':
        return armorType == 'light'; // 只能穿轻甲
      case 'Mage':
        return false; // 不能穿护甲
      case 'Cleric':
        return armorType == 'light' || armorType == 'medium'; // 轻甲和中甲
      default:
        return false;
    }
  }

  /// 获取技能列表
  static const Map<String, String> skillDescriptions = {
    'Physical': '体力相关技能：攀爬、游泳、跳跃等',
    'Subterfuge': '潜行相关技能：隐匿、开锁、偷窃等',
    'Knowledge': '知识相关技能：法术识别、历史、自然等',
    'Communication': '交流相关技能：说服、欺骗、威吓等',
  };

  /// 获取专精技能描述
  String get specialSkillDescription {
    return skillDescriptions[specialSkill] ?? '未知技能';
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'description': description,
      'armorRestriction': armorRestriction,
      'specialSkill': specialSkill,
      'startingGold': startingGold,
      'characteristics': characteristics,
    };
  }

  /// 从JSON创建职业
  factory CharacterClass.fromJson(Map<String, dynamic> json) {
    return CharacterClass(
      name: json['name'],
      displayName: json['displayName'],
      description: json['description'],
      armorRestriction: json['armorRestriction'],
      specialSkill: json['specialSkill'],
      startingGold: json['startingGold'],
      characteristics: json['characteristics'],
    );
  }

  @override
  String toString() {
    return 'CharacterClass(name: $name, displayName: $displayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CharacterClass && other.name == name;
  }

  @override
  int get hashCode => name.hashCode;
}
