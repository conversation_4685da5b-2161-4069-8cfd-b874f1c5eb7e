/// 设施数据模型
/// 表示广场中的各种设施
class Facility {
  final String id;
  final String name;
  final String displayName;
  final String icon;
  final String description;
  final bool isAvailable;

  const Facility({
    required this.id,
    required this.name,
    required this.displayName,
    required this.icon,
    required this.description,
    this.isAvailable = true,
  });

  /// 预定义的设施列表
  static const List<Facility> allFacilities = [
    Facility(
      id: 'shop',
      name: 'shop',
      displayName: '商店',
      icon: '🏪',
      description: '买卖装备和物品',
    ),
    Facility(
      id: 'temple',
      name: 'temple',
      displayName: '神殿',
      icon: '🏛️',
      description: '治疗和升级',
    ),
    Facility(
      id: 'portal',
      name: 'portal',
      displayName: '传送点',
      icon: '🌀',
      description: '传送到副本进行冒险',
    ),
  ];

  /// 根据ID获取设施
  static Facility? getFacilityById(String id) {
    try {
      return allFacilities.firstWhere((facility) => facility.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取可用设施列表
  static List<Facility> getAvailableFacilities() {
    return allFacilities.where((facility) => facility.isAvailable).toList();
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'icon': icon,
      'description': description,
      'isAvailable': isAvailable,
    };
  }

  /// 从JSON创建设施
  factory Facility.fromJson(Map<String, dynamic> json) {
    return Facility(
      id: json['id'],
      name: json['name'],
      displayName: json['displayName'],
      icon: json['icon'],
      description: json['description'],
      isAvailable: json['isAvailable'] ?? true,
    );
  }

  @override
  String toString() {
    return 'Facility(id: $id, displayName: $displayName, icon: $icon)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Facility && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
