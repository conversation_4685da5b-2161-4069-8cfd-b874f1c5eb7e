import 'package:flutter/material.dart';
import '../models/facility.dart';

/// 设施网格组件
/// 显示广场中的各种设施
class FacilityGrid extends StatelessWidget {
  final List<Facility> facilities;
  final Facility? selectedFacility;
  final Function(Facility) onFacilitySelected;

  const FacilityGrid({
    super.key,
    required this.facilities,
    this.selectedFacility,
    required this.onFacilitySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Text(
          '设施区域',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // 设施网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 1.2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: facilities.length,
          itemBuilder: (context, index) {
            final facility = facilities[index];
            final isSelected = selectedFacility?.id == facility.id;

            return _buildFacilityCard(context, facility, isSelected);
          },
        ),
      ],
    );
  }

  /// 构建设施卡片
  Widget _buildFacilityCard(BuildContext context, Facility facility, bool isSelected) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      transform: Matrix4.identity()..scale(isSelected ? 1.05 : 1.0),
      child: Card(
        elevation: isSelected ? 8 : 2,
        color: isSelected 
            ? Theme.of(context).colorScheme.primaryContainer
            : null,
        child: InkWell(
          onTap: () => onFacilitySelected(facility),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 设施图标
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.2)
                        : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: isSelected 
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      facility.icon,
                      style: const TextStyle(fontSize: 28),
                    ),
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // 设施名称
                Text(
                  facility.displayName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isSelected 
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : null,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 4),
                
                // 设施描述
                Text(
                  facility.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isSelected 
                        ? Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8)
                        : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                // 选中指示器
                if (isSelected) ...[
                  const SizedBox(height: 8),
                  Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
