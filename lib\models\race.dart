/// 种族数据模型
/// 基于Microlite20规则的种族系统
class Race {
  final String name;
  final String displayName;
  final String description;
  final String attributeBonus;
  final String advantage;
  final List<String> recommendedClasses;

  const Race({
    required this.name,
    required this.displayName,
    required this.description,
    required this.attributeBonus,
    required this.advantage,
    required this.recommendedClasses,
  });

  /// 预定义的种族列表
  static const List<Race> allRaces = [
    Race(
      name: 'Human',
      displayName: '人类',
      description: '适应性强的种族，在各个领域都有不错的表现',
      attributeBonus: '所有技能掷骰+1',
      advantage: '全能型，适合任何职业',
      recommendedClasses: ['Fighter', 'Rogue', 'Mage', 'Cleric'],
    ),
    Race(
      name: 'Elf',
      displayName: '精灵',
      description: '优雅而智慧的种族，天生具有魔法天赋',
      attributeBonus: 'MIND +2',
      advantage: '高智力，天生的施法者',
      recommendedClasses: ['Mage', 'Cleric'],
    ),
    Race(
      name: 'Dwarf',
      displayName: '矮人',
      description: '强壮而坚韧的种族，擅长战斗和工艺',
      attributeBonus: 'STR +2',
      advantage: '强壮有力，擅长近战',
      recommendedClasses: ['Fighter'],
    ),
    Race(
      name: 'Halfling',
      displayName: '半身人',
      description: '小巧而敏捷的种族，擅长潜行和灵活移动',
      attributeBonus: 'DEX +2',
      advantage: '敏捷灵活，擅长潜行',
      recommendedClasses: ['Rogue'],
    ),
  ];

  /// 根据名称获取种族
  static Race? getRaceByName(String name) {
    try {
      return allRaces.firstWhere((race) => race.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 获取属性加成值
  Map<String, int> getAttributeBonuses() {
    switch (name) {
      case 'Human':
        return {}; // 人类没有属性加成
      case 'Elf':
        return {'mind': 2};
      case 'Dwarf':
        return {'strength': 2};
      case 'Halfling':
        return {'dexterity': 2};
      default:
        return {};
    }
  }

  /// 检查是否推荐某个职业
  bool isRecommendedFor(String className) {
    return recommendedClasses.contains(className);
  }

  /// 获取种族图标
  String get icon {
    switch (name) {
      case 'Human':
        return '👤';
      case 'Elf':
        return '🧝';
      case 'Dwarf':
        return '🧔';
      case 'Halfling':
        return '🧒';
      default:
        return '❓';
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'displayName': displayName,
      'description': description,
      'attributeBonus': attributeBonus,
      'advantage': advantage,
      'recommendedClasses': recommendedClasses,
    };
  }

  /// 从JSON创建种族
  factory Race.fromJson(Map<String, dynamic> json) {
    return Race(
      name: json['name'],
      displayName: json['displayName'],
      description: json['description'],
      attributeBonus: json['attributeBonus'],
      advantage: json['advantage'],
      recommendedClasses: List<String>.from(json['recommendedClasses']),
    );
  }

  @override
  String toString() {
    return 'Race(name: $name, displayName: $displayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Race && other.name == name;
  }

  @override
  int get hashCode => name.hashCode;
}
