import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// 应用标题
  ///
  /// In zh, this message translates to:
  /// **'D&D游戏'**
  String get appTitle;

  /// 角色选择界面标题
  ///
  /// In zh, this message translates to:
  /// **'角色选择'**
  String get characterSelection;

  /// 角色创建界面标题
  ///
  /// In zh, this message translates to:
  /// **'创建角色'**
  String get characterCreation;

  /// 创建角色按钮
  ///
  /// In zh, this message translates to:
  /// **'创建角色'**
  String get createCharacter;

  /// 进入游戏按钮
  ///
  /// In zh, this message translates to:
  /// **'进入游戏'**
  String get enterGame;

  /// 删除按钮
  ///
  /// In zh, this message translates to:
  /// **'删除'**
  String get delete;

  /// 没有角色时的提示
  ///
  /// In zh, this message translates to:
  /// **'还没有角色'**
  String get noCharacters;

  /// 没有角色时的提示文本
  ///
  /// In zh, this message translates to:
  /// **'点击下方\"创建角色\"按钮开始创建你的第一个角色'**
  String get noCharactersHint;

  /// 基本信息标题
  ///
  /// In zh, this message translates to:
  /// **'1. 基本信息'**
  String get basicInfo;

  /// 种族选择标题
  ///
  /// In zh, this message translates to:
  /// **'2. 种族选择'**
  String get raceSelection;

  /// 职业选择标题
  ///
  /// In zh, this message translates to:
  /// **'3. 职业选择'**
  String get classSelection;

  /// 当前货币标题
  ///
  /// In zh, this message translates to:
  /// **'4. 当前货币'**
  String get currentCurrency;

  /// 属性与生命值标题
  ///
  /// In zh, this message translates to:
  /// **'5. 属性与生命值掷骰'**
  String get attributesAndHp;

  /// 角色姓名输入框标签
  ///
  /// In zh, this message translates to:
  /// **'角色姓名'**
  String get characterName;

  /// 性别标签
  ///
  /// In zh, this message translates to:
  /// **'性别：'**
  String get gender;

  /// 男性
  ///
  /// In zh, this message translates to:
  /// **'男'**
  String get male;

  /// 女性
  ///
  /// In zh, this message translates to:
  /// **'女'**
  String get female;

  /// 其他性别
  ///
  /// In zh, this message translates to:
  /// **'其他'**
  String get other;

  /// 年龄输入框标签
  ///
  /// In zh, this message translates to:
  /// **'年龄'**
  String get age;

  /// 人类种族
  ///
  /// In zh, this message translates to:
  /// **'人类'**
  String get raceHuman;

  /// 精灵种族
  ///
  /// In zh, this message translates to:
  /// **'精灵'**
  String get raceElf;

  /// 矮人种族
  ///
  /// In zh, this message translates to:
  /// **'矮人'**
  String get raceDwarf;

  /// 半身人种族
  ///
  /// In zh, this message translates to:
  /// **'半身人'**
  String get raceHalfling;

  /// 战士职业
  ///
  /// In zh, this message translates to:
  /// **'战士'**
  String get classFighter;

  /// 盗贼职业
  ///
  /// In zh, this message translates to:
  /// **'盗贼'**
  String get classRogue;

  /// 法师职业
  ///
  /// In zh, this message translates to:
  /// **'法师'**
  String get classMage;

  /// 牧师职业
  ///
  /// In zh, this message translates to:
  /// **'牧师'**
  String get classCleric;

  /// 生命值
  ///
  /// In zh, this message translates to:
  /// **'生命值'**
  String get hitPoints;

  /// 力量属性
  ///
  /// In zh, this message translates to:
  /// **'力量'**
  String get strength;

  /// 敏捷属性
  ///
  /// In zh, this message translates to:
  /// **'敏捷'**
  String get dexterity;

  /// 心智属性
  ///
  /// In zh, this message translates to:
  /// **'心智'**
  String get mind;

  /// 重新掷骰按钮
  ///
  /// In zh, this message translates to:
  /// **'重新掷骰所有属性和生命值'**
  String get rerollAttributes;

  /// 返回按钮
  ///
  /// In zh, this message translates to:
  /// **'返回'**
  String get back;

  /// 确认按钮
  ///
  /// In zh, this message translates to:
  /// **'确认'**
  String get confirm;

  /// 取消按钮
  ///
  /// In zh, this message translates to:
  /// **'取消'**
  String get cancel;

  /// 确认删除对话框标题
  ///
  /// In zh, this message translates to:
  /// **'确认删除'**
  String get confirmDelete;

  /// 删除角色确认文本
  ///
  /// In zh, this message translates to:
  /// **'确定要删除角色\"{name}\"吗？此操作无法撤销。'**
  String deleteCharacterConfirm(String name);

  /// 确认创建角色对话框标题
  ///
  /// In zh, this message translates to:
  /// **'确认创建角色'**
  String get confirmCreateCharacter;

  /// 返回修改按钮
  ///
  /// In zh, this message translates to:
  /// **'返回修改'**
  String get backToModify;

  /// 确认创建按钮
  ///
  /// In zh, this message translates to:
  /// **'确认创建'**
  String get confirmCreate;

  /// 等级
  ///
  /// In zh, this message translates to:
  /// **'等级'**
  String get level;

  /// 岁数单位
  ///
  /// In zh, this message translates to:
  /// **'岁'**
  String get yearsOld;

  /// 起始金币文本
  ///
  /// In zh, this message translates to:
  /// **'起始金币：{amount}gp ({className})'**
  String startingGold(int amount, String className);

  /// 选中种族文本
  ///
  /// In zh, this message translates to:
  /// **'选中种族：{raceName}'**
  String selectedRace(String raceName);

  /// 选中职业文本
  ///
  /// In zh, this message translates to:
  /// **'选中职业：{className}'**
  String selectedClass(String className);

  /// 属性加成标签
  ///
  /// In zh, this message translates to:
  /// **'属性加成'**
  String get attributeBonus;

  /// 种族优势标签
  ///
  /// In zh, this message translates to:
  /// **'种族优势'**
  String get racialAdvantage;

  /// 推荐职业标签
  ///
  /// In zh, this message translates to:
  /// **'推荐职业：'**
  String get recommendedClasses;

  /// 护甲限制标签
  ///
  /// In zh, this message translates to:
  /// **'护甲限制'**
  String get armorRestriction;

  /// 专精技能标签
  ///
  /// In zh, this message translates to:
  /// **'专精技能'**
  String get specialSkill;

  /// 职业特点标签
  ///
  /// In zh, this message translates to:
  /// **'职业特点'**
  String get classCharacteristics;

  /// 技能说明标签
  ///
  /// In zh, this message translates to:
  /// **'技能说明：'**
  String get skillDescription;

  /// 请输入角色姓名错误提示
  ///
  /// In zh, this message translates to:
  /// **'请输入角色姓名'**
  String get pleaseEnterName;

  /// 角色姓名过长错误提示
  ///
  /// In zh, this message translates to:
  /// **'角色姓名不能超过20个字符'**
  String get nameTooLong;

  /// 请输入年龄错误提示
  ///
  /// In zh, this message translates to:
  /// **'请输入年龄'**
  String get pleaseEnterAge;

  /// 无效年龄错误提示
  ///
  /// In zh, this message translates to:
  /// **'请输入有效年龄(1-1000)'**
  String get invalidAge;

  /// 请选择种族错误提示
  ///
  /// In zh, this message translates to:
  /// **'请选择种族'**
  String get pleaseSelectRace;

  /// 请选择职业错误提示
  ///
  /// In zh, this message translates to:
  /// **'请选择职业'**
  String get pleaseSelectClass;

  /// 属性数据异常错误提示
  ///
  /// In zh, this message translates to:
  /// **'属性数据异常，请重新掷骰'**
  String get attributeDataError;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
