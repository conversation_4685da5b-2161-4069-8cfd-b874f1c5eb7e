import 'package:flutter/material.dart';
import '../models/facility.dart';
import '../models/player_status.dart';

/// 操作区组件
/// 显示广场场景特有的操作按钮
class ActionBar extends StatelessWidget {
  final Facility? selectedFacility;
  final PlayerStatus playerStatus;
  final bool isLoading;
  final VoidCallback onEnterFacility;
  final VoidCallback onOpenSpellList;
  final VoidCallback onOpenInventory;
  final VoidCallback onOpenEquipment;

  const ActionBar({
    super.key,
    this.selectedFacility,
    required this.playerStatus,
    this.isLoading = false,
    required this.onEnterFacility,
    required this.onOpenSpellList,
    required this.onOpenInventory,
    required this.onOpenEquipment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 进入按钮（当选中设施时显示）
          if (selectedFacility != null) ...[
            Expanded(
              child: ElevatedButton.icon(
                onPressed: isLoading ? null : onEnterFacility,
                icon: isLoading 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.login),
                label: Text(isLoading ? '进入中...' : '进入${selectedFacility!.displayName}'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
          ],
          
          // 法术按钮
          Expanded(
            child: _buildActionButton(
              context,
              icon: '✨',
              label: '法术',
              onPressed: onOpenSpellList,
              isEnabled: _canUseMagic(),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 道具按钮
          Expanded(
            child: _buildActionButton(
              context,
              icon: '🎒',
              label: '道具',
              onPressed: onOpenInventory,
              isEnabled: true,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 装备按钮
          Expanded(
            child: _buildActionButton(
              context,
              icon: '🛡️',
              label: '装备',
              onPressed: onOpenEquipment,
              isEnabled: true,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(
    BuildContext context, {
    required String icon,
    required String label,
    required VoidCallback onPressed,
    required bool isEnabled,
  }) {
    return ElevatedButton(
      onPressed: isEnabled ? onPressed : null,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 12),
        backgroundColor: isEnabled 
            ? null 
            : Colors.grey.withOpacity(0.3),
        foregroundColor: isEnabled 
            ? null 
            : Colors.grey,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            icon,
            style: TextStyle(
              fontSize: 20,
              color: isEnabled ? null : Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isEnabled ? null : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 检查是否可以使用法术
  bool _canUseMagic() {
    return playerStatus.character.characterClass.name == 'Mage' ||
           playerStatus.character.characterClass.name == 'Cleric';
  }
}
