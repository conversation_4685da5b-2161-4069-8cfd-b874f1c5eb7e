{"@@locale": "en", "appTitle": "D&D Game", "characterSelection": "Character Selection", "characterCreation": "Create Character", "createCharacter": "Create Character", "enterGame": "Enter Game", "delete": "Delete", "noCharacters": "No Characters", "noCharactersHint": "Click the \"Create Character\" button below to create your first character", "basicInfo": "1. Basic Information", "raceSelection": "2. Race Selection", "classSelection": "3. Class Selection", "currentCurrency": "4. <PERSON><PERSON>", "attributesAndHp": "5. Attributes & Hit Points", "characterName": "Character Name", "gender": "Gender:", "male": "Male", "female": "Female", "other": "Other", "age": "Age", "raceHuman": "Human", "raceElf": "<PERSON><PERSON>", "raceDwarf": "<PERSON><PERSON><PERSON>", "raceHalfling": "<PERSON><PERSON>", "classFighter": "Fighter", "classRogue": "Rogue", "classMage": "Mage", "classCleric": "Cleric", "hitPoints": "Hit Points", "strength": "Strength", "dexterity": "Dexterity", "mind": "Mind", "rerollAttributes": "Reroll All Attributes and Hit Points", "back": "Back", "confirm": "Confirm", "cancel": "Cancel", "confirmDelete": "Confirm Delete", "deleteCharacterConfirm": "Are you sure you want to delete character \"{name}\"? This action cannot be undone.", "confirmCreateCharacter": "Confirm Create Character", "backToModify": "Back to Modify", "confirmCreate": "Confirm Create", "level": "Level", "yearsOld": "years old", "startingGold": "Starting Gold: {amount}gp ({className})", "selectedRace": "Selected Race: {raceName}", "selectedClass": "Selected Class: {className}", "attributeBonus": "Attribute Bonus", "racialAdvantage": "Racial Advantage", "recommendedClasses": "Recommended Classes:", "armorRestriction": "Armor Restriction", "specialSkill": "Special Skill", "classCharacteristics": "Class Characteristics", "skillDescription": "Skill Description:", "pleaseEnterName": "Please enter character name", "nameTooLong": "Character name cannot exceed 20 characters", "pleaseEnterAge": "Please enter age", "invalidAge": "Please enter valid age (1-1000)", "pleaseSelectRace": "Please select a race", "pleaseSelectClass": "Please select a class", "attributeDataError": "Attribute data error, please reroll"}