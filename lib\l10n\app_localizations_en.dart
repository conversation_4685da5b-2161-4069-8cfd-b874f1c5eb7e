// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'D&D Game';

  @override
  String get characterSelection => 'Character Selection';

  @override
  String get characterCreation => 'Create Character';

  @override
  String get createCharacter => 'Create Character';

  @override
  String get enterGame => 'Enter Game';

  @override
  String get delete => 'Delete';

  @override
  String get noCharacters => 'No Characters';

  @override
  String get noCharactersHint =>
      'Click the \"Create Character\" button below to create your first character';

  @override
  String get basicInfo => '1. Basic Information';

  @override
  String get raceSelection => '2. Race Selection';

  @override
  String get classSelection => '3. Class Selection';

  @override
  String get currentCurrency => '4. Current Currency';

  @override
  String get attributesAndHp => '5. Attributes & Hit Points';

  @override
  String get characterName => 'Character Name';

  @override
  String get gender => 'Gender:';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get other => 'Other';

  @override
  String get age => 'Age';

  @override
  String get raceHuman => 'Human';

  @override
  String get raceElf => 'Elf';

  @override
  String get raceDwarf => 'Dwarf';

  @override
  String get raceHalfling => 'Halfling';

  @override
  String get classFighter => 'Fighter';

  @override
  String get classRogue => 'Rogue';

  @override
  String get classMage => 'Mage';

  @override
  String get classCleric => 'Cleric';

  @override
  String get hitPoints => 'Hit Points';

  @override
  String get strength => 'Strength';

  @override
  String get dexterity => 'Dexterity';

  @override
  String get mind => 'Mind';

  @override
  String get rerollAttributes => 'Reroll All Attributes and Hit Points';

  @override
  String get back => 'Back';

  @override
  String get confirm => 'Confirm';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String deleteCharacterConfirm(String name) {
    return 'Are you sure you want to delete character \"$name\"? This action cannot be undone.';
  }

  @override
  String get confirmCreateCharacter => 'Confirm Create Character';

  @override
  String get backToModify => 'Back to Modify';

  @override
  String get confirmCreate => 'Confirm Create';

  @override
  String get level => 'Level';

  @override
  String get yearsOld => 'years old';

  @override
  String startingGold(int amount, String className) {
    return 'Starting Gold: ${amount}gp ($className)';
  }

  @override
  String selectedRace(String raceName) {
    return 'Selected Race: $raceName';
  }

  @override
  String selectedClass(String className) {
    return 'Selected Class: $className';
  }

  @override
  String get attributeBonus => 'Attribute Bonus';

  @override
  String get racialAdvantage => 'Racial Advantage';

  @override
  String get recommendedClasses => 'Recommended Classes:';

  @override
  String get armorRestriction => 'Armor Restriction';

  @override
  String get specialSkill => 'Special Skill';

  @override
  String get classCharacteristics => 'Class Characteristics';

  @override
  String get skillDescription => 'Skill Description:';

  @override
  String get pleaseEnterName => 'Please enter character name';

  @override
  String get nameTooLong => 'Character name cannot exceed 20 characters';

  @override
  String get pleaseEnterAge => 'Please enter age';

  @override
  String get invalidAge => 'Please enter valid age (1-1000)';

  @override
  String get pleaseSelectRace => 'Please select a race';

  @override
  String get pleaseSelectClass => 'Please select a class';

  @override
  String get attributeDataError => 'Attribute data error, please reroll';
}
