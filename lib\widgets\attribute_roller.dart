import 'package:flutter/material.dart';
import '../models/attributes.dart';

/// 属性掷骰组件
/// 显示角色属性和生命值，支持重新掷骰
class AttributeRoller extends StatelessWidget {
  final Attributes attributes;
  final VoidCallback onReroll;

  const AttributeRoller({
    super.key,
    required this.attributes,
    required this.onReroll,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 生命值显示
        _buildHitPointsSection(context),
        const SizedBox(height: 16),
        
        // 属性显示
        _buildAttributesSection(context),
        const SizedBox(height: 16),
        
        // 重新掷骰按钮
        _buildRerollButton(context),
      ],
    );
  }

  /// 构建生命值区域
  Widget _buildHitPointsSection(BuildContext context) {
    final totalHp = attributes.strength + attributes.hpBonus;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.favorite,
            color: Colors.red,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '生命值',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$totalHp (${attributes.strength}+${attributes.hpBonus})',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          // 生命值数值
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              totalHp.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建属性区域
  Widget _buildAttributesSection(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildAttributeCard(
            context,
            'STR',
            '力量',
            attributes.strength,
            attributes.strengthModifier,
            Colors.red,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildAttributeCard(
            context,
            'DEX',
            '敏捷',
            attributes.dexterity,
            attributes.dexterityModifier,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildAttributeCard(
            context,
            'MIND',
            '心智',
            attributes.mind,
            attributes.mindModifier,
            Colors.blue,
          ),
        ),
      ],
    );
  }

  /// 构建单个属性卡片
  Widget _buildAttributeCard(
    BuildContext context,
    String shortName,
    String fullName,
    int value,
    int modifier,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          // 属性简称
          Text(
            shortName,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          // 属性全名
          Text(
            fullName,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 8),
          // 属性值
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                value.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 4),
          // 属性加成
          Text(
            modifier >= 0 ? '+$modifier' : '$modifier',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: modifier >= 0 ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建重新掷骰按钮
  Widget _buildRerollButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onReroll,
        icon: const Icon(Icons.casino),
        label: const Text('重新掷骰所有属性和生命值'),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
          side: BorderSide(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ),
    );
  }
}
