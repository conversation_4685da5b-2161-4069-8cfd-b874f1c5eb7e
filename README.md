# D&D Game - 角色选择系统

这是一个基于Flutter开发的龙与地下城RPG游戏项目，使用Microlite20规则系统。本项目实现了完整的角色创建和选择系统。

## 功能特性

### 已实现功能

#### 角色选择系统
- 角色列表显示
- 角色选择和删除
- 本地数据持久化
- 角色信息卡片展示

#### 角色创建系统
- 基本信息输入（姓名、性别、年龄）
- 种族选择（人类、精灵、矮人、半身人）
- 职业选择（战士、盗贼、法师、牧师）
- 属性掷骰（STR、DEX、MIND）
- 生命值计算
- 种族加成自动应用
- 角色信息确认

#### 游戏规则实现
- Microlite20规则系统
- 3d6属性生成
- 种族特性加成
- 职业专精技能
- 护甲等级计算
- 攻击加成计算
- 起始金币分配

## 项目结构

```
lib/
├── main.dart                    # 应用入口
├── models/                      # 数据模型
│   ├── character.dart          # 角色模型
│   ├── attributes.dart         # 属性模型
│   ├── race.dart              # 种族模型
│   └── character_class.dart    # 职业模型
├── services/                   # 服务层
│   ├── character_service.dart  # 角色数据管理
│   ├── dice_service.dart       # 掷骰子服务
│   └── logger_service.dart     # 日志服务
├── screens/                    # 页面
│   ├── character_selection_screen.dart  # 角色选择界面
│   └── character_creation_screen.dart   # 角色创建界面
└── widgets/                    # UI组件
    ├── character_card.dart     # 角色卡片
    ├── race_selector.dart      # 种族选择器
    ├── class_selector.dart     # 职业选择器
    └── attribute_roller.dart   # 属性掷骰器
```

## 技术栈

- **框架**: Flutter 3.0+
- **状态管理**: Provider
- **本地存储**: SharedPreferences
- **日志**: Logger
- **测试**: Flutter Test

## 运行项目

### 环境要求
- Flutter SDK 3.0+
- Dart SDK 3.0+

### 安装依赖
```bash
flutter pub get
```

### 运行应用
```bash
flutter run
```

### 运行测试
```bash
flutter test
```

## 游戏规则说明

### Microlite20规则
本项目基于简化的D&D规则Microlite20，主要特点：

#### 属性系统
- **STR (力量)**: 影响近战攻击、伤害和体力相关检定
- **DEX (敏捷)**: 影响远程攻击、护甲等级和灵活性检定
- **MIND (心智)**: 影响魔法攻击、法术DC和知识相关检定

#### 种族特性
- **人类**: 所有技能掷骰+1
- **精灵**: MIND +2，天生的施法者
- **矮人**: STR +2，强壮有力
- **半身人**: DEX +2，敏捷灵活

#### 职业特性
- **战士**: Physical +3，可穿任何护甲，起始150gp
- **盗贼**: Subterfuge +3，只能穿轻甲，起始125gp
- **法师**: Knowledge +3，不能穿护甲，起始75gp
- **牧师**: Communication +3，可穿轻甲和中甲，起始120gp

#### 计算公式
- **属性加成** = (属性值-10)/2，向下取整
- **生命值** = STR属性值 + 1d6奖励
- **护甲等级** = 10 + DEX加成 + 护甲加成
- **攻击加成** = 属性加成 + 等级

## 开发规范

### 代码风格
- 遵循Dart官方代码风格
- 使用有意义的变量和函数名
- 添加必要的注释和文档

### 日志系统
- 使用统一的LoggerService
- 记录界面进入/退出事件
- 记录用户操作和数据变更
- 记录错误和异常信息

### 测试
- 为核心功能编写单元测试
- 测试数据模型的序列化/反序列化
- 测试游戏规则计算逻辑
- 测试角色创建和验证流程

## 下一步开发计划

1. **游戏主界面**: 实现角色进入游戏后的主界面
2. **装备系统**: 实现武器、护甲、物品管理
3. **战斗系统**: 实现回合制战斗机制
4. **法术系统**: 实现法师和牧师的法术功能
5. **副本系统**: 实现LLM驱动的动态剧情生成
6. **图像生成**: 集成ComfyUI进行场景图片生成

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。
