import 'character.dart';

/// 玩家状态数据模型
/// 表示玩家在游戏中的实时状态
class PlayerStatus {
  final Character character;
  int currentHp;
  int currentMp;
  String location;
  bool isOnline;
  DateTime lastActiveTime;

  PlayerStatus({
    required this.character,
    int? currentHp,
    int? currentMp,
    this.location = 'square',
    this.isOnline = true,
    DateTime? lastActiveTime,
  }) : currentHp = currentHp ?? character.maxHitPoints,
       currentMp = currentMp ?? _calculateMaxMp(character),
       lastActiveTime = lastActiveTime ?? DateTime.now();

  /// 计算最大魔法值
  static int _calculateMaxMp(Character character) {
    // 基于心智属性计算魔法值
    // 法师和牧师有更多魔法值
    int baseMp = character.attributes.mind;
    
    switch (character.characterClass.name) {
      case 'Mage':
        return baseMp + character.level * 2; // 法师每级+2MP
      case 'Cleric':
        return baseMp + character.level; // 牧师每级+1MP
      case 'Fighter':
      case 'Rogue':
        return (baseMp / 2).floor(); // 战士和盗贼MP减半
      default:
        return baseMp;
    }
  }

  /// 获取最大生命值
  int get maxHp => character.maxHitPoints;

  /// 获取最大魔法值
  int get maxMp => _calculateMaxMp(character);

  /// 获取生命值百分比
  double get hpPercentage => currentHp / maxHp;

  /// 获取魔法值百分比
  double get mpPercentage => maxMp > 0 ? currentMp / maxMp : 0.0;

  /// 是否满血
  bool get isFullHp => currentHp >= maxHp;

  /// 是否满魔法
  bool get isFullMp => currentMp >= maxMp;

  /// 是否濒死
  bool get isNearDeath => hpPercentage <= 0.25;

  /// 是否魔法不足
  bool get isLowMp => mpPercentage <= 0.25;

  /// 治疗生命值
  void heal(int amount) {
    currentHp = (currentHp + amount).clamp(0, maxHp);
  }

  /// 恢复魔法值
  void restoreMp(int amount) {
    currentMp = (currentMp + amount).clamp(0, maxMp);
  }

  /// 受到伤害
  void takeDamage(int damage) {
    currentHp = (currentHp - damage).clamp(0, maxHp);
  }

  /// 消耗魔法
  bool consumeMp(int cost) {
    if (currentMp >= cost) {
      currentMp -= cost;
      return true;
    }
    return false;
  }

  /// 完全恢复
  void fullRestore() {
    currentHp = maxHp;
    currentMp = maxMp;
  }

  /// 更新位置
  void updateLocation(String newLocation) {
    location = newLocation;
    lastActiveTime = DateTime.now();
  }

  /// 更新在线状态
  void updateOnlineStatus(bool online) {
    isOnline = online;
    if (online) {
      lastActiveTime = DateTime.now();
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'character': character.toJson(),
      'currentHp': currentHp,
      'currentMp': currentMp,
      'location': location,
      'isOnline': isOnline,
      'lastActiveTime': lastActiveTime.toIso8601String(),
    };
  }

  /// 从JSON创建玩家状态
  factory PlayerStatus.fromJson(Map<String, dynamic> json) {
    return PlayerStatus(
      character: Character.fromJson(json['character']),
      currentHp: json['currentHp'],
      currentMp: json['currentMp'],
      location: json['location'] ?? 'square',
      isOnline: json['isOnline'] ?? true,
      lastActiveTime: DateTime.parse(json['lastActiveTime']),
    );
  }

  /// 创建副本
  PlayerStatus copyWith({
    Character? character,
    int? currentHp,
    int? currentMp,
    String? location,
    bool? isOnline,
    DateTime? lastActiveTime,
  }) {
    return PlayerStatus(
      character: character ?? this.character,
      currentHp: currentHp ?? this.currentHp,
      currentMp: currentMp ?? this.currentMp,
      location: location ?? this.location,
      isOnline: isOnline ?? this.isOnline,
      lastActiveTime: lastActiveTime ?? this.lastActiveTime,
    );
  }

  @override
  String toString() {
    return 'PlayerStatus(character: ${character.name}, hp: $currentHp/$maxHp, mp: $currentMp/$maxMp, location: $location)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlayerStatus && other.character.id == character.id;
  }

  @override
  int get hashCode => character.id.hashCode;
}
