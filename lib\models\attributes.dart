/// 角色属性数据模型
/// 基于Microlite20规则的三大属性系统
class Attributes {
  int strength;    // STR - 力量
  int dexterity;   // DEX - 敏捷  
  int mind;        // MIND - 心智
  int hpBonus;     // 等级奖励HP (每级1d6)

  Attributes({
    required this.strength,
    required this.dexterity,
    required this.mind,
    this.hpBonus = 0,
  });

  /// 计算属性加成
  static int getModifier(int attributeValue) {
    return ((attributeValue - 10) / 2).floor();
  }

  /// 获取力量加成
  int get strengthModifier => getModifier(strength);

  /// 获取敏捷加成
  int get dexterityModifier => getModifier(dexterity);

  /// 获取心智加成
  int get mindModifier => getModifier(mind);

  /// 应用种族加成
  void applyRacialBonus(String raceName) {
    switch (raceName) {
      case 'Human':
        // 人类没有属性加成，但有技能加成
        break;
      case 'Elf':
        mind += 2; // 精灵 MIND +2
        break;
      case 'Dwarf':
        strength += 2; // 矮人 STR +2
        break;
      case 'Halfling':
        dexterity += 2; // 半身人 DEX +2
        break;
    }
  }

  /// 移除种族加成（用于切换种族时）
  void removeRacialBonus(String raceName) {
    switch (raceName) {
      case 'Human':
        // 人类没有属性加成
        break;
      case 'Elf':
        mind -= 2;
        break;
      case 'Dwarf':
        strength -= 2;
        break;
      case 'Halfling':
        dexterity -= 2;
        break;
    }
  }

  /// 验证属性值是否有效
  bool isValid() {
    return strength >= 3 && strength <= 18 &&
           dexterity >= 3 && dexterity <= 18 &&
           mind >= 3 && mind <= 18 &&
           hpBonus >= 0;
  }

  /// 获取属性总和
  int get totalPoints => strength + dexterity + mind;

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'strength': strength,
      'dexterity': dexterity,
      'mind': mind,
      'hpBonus': hpBonus,
    };
  }

  /// 从JSON创建属性
  factory Attributes.fromJson(Map<String, dynamic> json) {
    return Attributes(
      strength: json['strength'],
      dexterity: json['dexterity'],
      mind: json['mind'],
      hpBonus: json['hpBonus'] ?? 0,
    );
  }

  /// 创建属性副本
  Attributes copyWith({
    int? strength,
    int? dexterity,
    int? mind,
    int? hpBonus,
  }) {
    return Attributes(
      strength: strength ?? this.strength,
      dexterity: dexterity ?? this.dexterity,
      mind: mind ?? this.mind,
      hpBonus: hpBonus ?? this.hpBonus,
    );
  }

  @override
  String toString() {
    return 'Attributes(STR: $strength(${strengthModifier >= 0 ? '+' : ''}$strengthModifier), '
           'DEX: $dexterity(${dexterityModifier >= 0 ? '+' : ''}$dexterityModifier), '
           'MIND: $mind(${mindModifier >= 0 ? '+' : ''}$mindModifier))';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Attributes &&
           other.strength == strength &&
           other.dexterity == dexterity &&
           other.mind == mind &&
           other.hpBonus == hpBonus;
  }

  @override
  int get hashCode {
    return strength.hashCode ^
           dexterity.hashCode ^
           mind.hashCode ^
           hpBonus.hashCode;
  }
}
