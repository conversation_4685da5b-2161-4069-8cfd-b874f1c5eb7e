import 'package:flutter/material.dart';
import '../models/character.dart';
import '../models/attributes.dart';
import '../models/race.dart';
import '../models/character_class.dart';
import '../services/character_service.dart';
import '../services/dice_service.dart';
import '../services/logger_service.dart';
import '../widgets/race_selector.dart';
import '../widgets/class_selector.dart';
import '../widgets/attribute_roller.dart';

/// 角色创建界面
/// 提供完整的角色创建流程，包括基本信息、种族、职业、属性设置
class CharacterCreationScreen extends StatefulWidget {
  const CharacterCreationScreen({super.key});

  @override
  State<CharacterCreationScreen> createState() => _CharacterCreationScreenState();
}

class _CharacterCreationScreenState extends State<CharacterCreationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();

  String _selectedGender = '男';
  Race? _selectedRace;
  CharacterClass? _selectedClass;
  Attributes? _attributes;
  bool _isCreating = false;

  @override
  void initState() {
    super.initState();
    LoggerService.enterScreen('CharacterCreationScreen');
    _initializeAttributes();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    LoggerService.exitScreen('CharacterCreationScreen');
    super.dispose();
  }

  /// 初始化属性
  void _initializeAttributes() {
    _attributes = CharacterService.rerollAttributes();
    setState(() {});
  }

  /// 重新掷骰属性
  void _rerollAttributes() {
    LoggerService.userAction('CharacterCreationScreen', 'reroll_attributes');
    setState(() {
      _attributes = CharacterService.rerollAttributes();
    });
  }

  /// 选择种族
  void _selectRace(Race race) {
    LoggerService.userAction('CharacterCreationScreen', 'select_race', {
      'raceName': race.name,
    });

    setState(() {
      // 如果之前有选择种族，先移除加成
      if (_selectedRace != null && _attributes != null) {
        _attributes!.removeRacialBonus(_selectedRace!.name);
      }
      
      _selectedRace = race;
      
      // 应用新种族加成
      if (_attributes != null) {
        _attributes!.applyRacialBonus(race.name);
      }
    });
  }

  /// 选择职业
  void _selectClass(CharacterClass characterClass) {
    LoggerService.userAction('CharacterCreationScreen', 'select_class', {
      'className': characterClass.name,
    });

    setState(() {
      _selectedClass = characterClass;
    });
  }

  /// 创建角色
  Future<void> _createCharacter() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedRace == null) {
      _showErrorSnackBar('请选择种族');
      return;
    }

    if (_selectedClass == null) {
      _showErrorSnackBar('请选择职业');
      return;
    }

    if (_attributes == null) {
      _showErrorSnackBar('属性数据异常，请重新掷骰');
      return;
    }

    // 显示确认对话框
    final confirmed = await _showCreateConfirmDialog();
    if (!confirmed) return;

    setState(() {
      _isCreating = true;
    });

    try {
      LoggerService.userAction('CharacterCreationScreen', 'create_character_confirm', {
        'name': _nameController.text.trim(),
        'gender': _selectedGender,
        'age': int.parse(_ageController.text),
        'race': _selectedRace!.name,
        'class': _selectedClass!.name,
      });

      final character = CharacterService.createCharacter(
        name: _nameController.text.trim(),
        gender: _selectedGender,
        age: int.parse(_ageController.text),
        race: _selectedRace!,
        characterClass: _selectedClass!,
        attributes: _attributes!,
      );

      final success = await CharacterService.saveCharacter(character);
      
      if (success) {
        LoggerService.info(LogTags.character, 'Character created and saved successfully');
        if (mounted) {
          Navigator.of(context).pop(character);
        }
      } else {
        _showErrorSnackBar('保存角色失败，请重试');
      }
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.character, 'Failed to create character', e, stackTrace);
      _showErrorSnackBar('创建角色失败，请重试');
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  /// 显示创建确认对话框
  Future<bool> _showCreateConfirmDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认创建角色'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('角色名称：${_nameController.text.trim()}'),
              Text('性别：$_selectedGender'),
              Text('年龄：${_ageController.text}岁'),
              Text('种族：${_selectedRace!.displayName}'),
              Text('职业：${_selectedClass!.displayName}'),
              const SizedBox(height: 8),
              const Text('属性：', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('力量：${_attributes!.strength} (${_attributes!.strengthModifier >= 0 ? '+' : ''}${_attributes!.strengthModifier})'),
              Text('敏捷：${_attributes!.dexterity} (${_attributes!.dexterityModifier >= 0 ? '+' : ''}${_attributes!.dexterityModifier})'),
              Text('心智：${_attributes!.mind} (${_attributes!.mindModifier >= 0 ? '+' : ''}${_attributes!.mindModifier})'),
              const SizedBox(height: 8),
              Text('最大生命值：${_attributes!.strength + _attributes!.hpBonus}'),
              Text('护甲等级：${10 + _attributes!.dexterityModifier}'),
              Text('起始金币：${_selectedClass!.startingGold}gp'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('返回修改'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确认创建'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 显示错误消息
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('创建角色'),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // 内容区域
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 1. 基本信息
                    _buildBasicInfoSection(),
                    const SizedBox(height: 24),
                    
                    // 2. 种族选择
                    _buildRaceSection(),
                    const SizedBox(height: 24),
                    
                    // 3. 职业选择
                    _buildClassSection(),
                    const SizedBox(height: 24),
                    
                    // 4. 当前货币
                    _buildCurrencySection(),
                    const SizedBox(height: 24),
                    
                    // 5. 属性与生命值掷骰
                    _buildAttributesSection(),
                  ],
                ),
              ),
            ),
            
            // 底部操作栏
            _buildActionBar(),
          ],
        ),
      ),
    );
  }

  /// 构建基本信息区域
  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '1. 基本信息',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            const SizedBox(height: 8),
            
            // 角色姓名
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '角色姓名',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '请输入角色姓名';
                }
                if (value.trim().length > 20) {
                  return '角色姓名不能超过20个字符';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // 性别选择
            Row(
              children: [
                const Text('性别：'),
                const SizedBox(width: 16),
                ...['男', '女', '其他'].map((gender) => Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Radio<String>(
                      value: gender,
                      groupValue: _selectedGender,
                      onChanged: (value) {
                        setState(() {
                          _selectedGender = value!;
                        });
                      },
                    ),
                    Text(gender),
                    const SizedBox(width: 16),
                  ],
                )),
              ],
            ),
            const SizedBox(height: 16),
            
            // 年龄
            SizedBox(
              width: 120,
              child: TextFormField(
                controller: _ageController,
                decoration: const InputDecoration(
                  labelText: '年龄',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入年龄';
                  }
                  final age = int.tryParse(value);
                  if (age == null || age < 1 || age > 1000) {
                    return '请输入有效年龄(1-1000)';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建种族选择区域
  Widget _buildRaceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '2. 种族选择',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            const SizedBox(height: 8),
            
            RaceSelector(
              selectedRace: _selectedRace,
              onRaceSelected: _selectRace,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建职业选择区域
  Widget _buildClassSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '3. 职业选择',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            const SizedBox(height: 8),
            
            ClassSelector(
              selectedClass: _selectedClass,
              onClassSelected: _selectClass,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建货币区域
  Widget _buildCurrencySection() {
    if (_selectedClass == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '4. 当前货币',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            const SizedBox(height: 8),
            
            Row(
              children: [
                const Icon(Icons.monetization_on, color: Colors.amber),
                const SizedBox(width: 8),
                Text(
                  '起始金币：${_selectedClass!.startingGold}gp (${_selectedClass!.displayName})',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建属性区域
  Widget _buildAttributesSection() {
    if (_attributes == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '5. 属性与生命值掷骰',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            const SizedBox(height: 8),
            
            AttributeRoller(
              attributes: _attributes!,
              onReroll: _rerollAttributes,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildActionBar() {
    final canCreate = _nameController.text.trim().isNotEmpty &&
                     _ageController.text.trim().isNotEmpty &&
                     _selectedRace != null &&
                     _selectedClass != null &&
                     _attributes != null;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 返回按钮
          Expanded(
            child: OutlinedButton(
              onPressed: _isCreating ? null : () => Navigator.of(context).pop(),
              child: const Text('返回'),
            ),
          ),
          const SizedBox(width: 16),
          // 创建按钮
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: canCreate && !_isCreating ? _createCharacter : null,
              child: _isCreating
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('创建角色'),
            ),
          ),
        ],
      ),
    );
  }
}
