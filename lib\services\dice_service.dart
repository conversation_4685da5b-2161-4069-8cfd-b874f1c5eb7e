import 'dart:math';

/// 掷骰子服务
/// 提供各种掷骰子功能，用于角色创建和游戏机制
class DiceService {
  static final Random _random = Random();

  /// 掷一个指定面数的骰子
  static int rollDie(int sides) {
    assert(sides > 0, '骰子面数必须大于0');
    return _random.nextInt(sides) + 1;
  }

  /// 掷多个骰子并返回总和
  static int rollDice(int count, int sides) {
    assert(count > 0, '骰子数量必须大于0');
    assert(sides > 0, '骰子面数必须大于0');
    
    int total = 0;
    for (int i = 0; i < count; i++) {
      total += rollDie(sides);
    }
    return total;
  }

  /// 掷3d6生成属性值
  static int rollAttribute() {
    return rollDice(3, 6);
  }

  /// 掷1d6生成HP奖励
  static int rollHpBonus() {
    return rollDie(6);
  }

  /// 生成完整的属性组
  static Map<String, int> rollAllAttributes() {
    return {
      'strength': rollAttribute(),
      'dexterity': rollAttribute(),
      'mind': rollAttribute(),
    };
  }

  /// 掷骰子并返回详细结果
  static DiceRollResult rollWithDetails(int count, int sides, {int modifier = 0}) {
    assert(count > 0, '骰子数量必须大于0');
    assert(sides > 0, '骰子面数必须大于0');
    
    List<int> rolls = [];
    for (int i = 0; i < count; i++) {
      rolls.add(rollDie(sides));
    }
    
    int sum = rolls.reduce((a, b) => a + b);
    int total = sum + modifier;
    
    return DiceRollResult(
      rolls: rolls,
      sum: sum,
      modifier: modifier,
      total: total,
      notation: '${count}d$sides${modifier != 0 ? (modifier > 0 ? '+$modifier' : '$modifier') : ''}',
    );
  }

  /// 生成属性值并返回详细结果
  static AttributeRollResult rollAttributeWithDetails() {
    DiceRollResult result = rollWithDetails(3, 6);
    return AttributeRollResult(
      diceResult: result,
      attributeValue: result.total,
      modifier: ((result.total - 10) / 2).floor(),
    );
  }

  /// 生成所有属性的详细结果
  static Map<String, AttributeRollResult> rollAllAttributesWithDetails() {
    return {
      'strength': rollAttributeWithDetails(),
      'dexterity': rollAttributeWithDetails(),
      'mind': rollAttributeWithDetails(),
    };
  }

  /// 计算生命值（基础HP + 1d6奖励）
  static HpRollResult rollHitPoints(int baseHp) {
    DiceRollResult bonusRoll = rollWithDetails(1, 6);
    int totalHp = baseHp + bonusRoll.total;
    
    return HpRollResult(
      baseHp: baseHp,
      bonusRoll: bonusRoll,
      totalHp: totalHp,
    );
  }

  /// 解析骰子表达式 (如 "2d6+3")
  static DiceRollResult? parseAndRoll(String expression) {
    // 简单的骰子表达式解析
    RegExp regex = RegExp(r'^(\d+)d(\d+)([+-]\d+)?$');
    Match? match = regex.firstMatch(expression.toLowerCase().replaceAll(' ', ''));
    
    if (match == null) return null;
    
    int count = int.parse(match.group(1)!);
    int sides = int.parse(match.group(2)!);
    int modifier = 0;
    
    if (match.group(3) != null) {
      modifier = int.parse(match.group(3)!);
    }
    
    return rollWithDetails(count, sides, modifier: modifier);
  }
}

/// 骰子掷骰结果
class DiceRollResult {
  final List<int> rolls;
  final int sum;
  final int modifier;
  final int total;
  final String notation;

  const DiceRollResult({
    required this.rolls,
    required this.sum,
    required this.modifier,
    required this.total,
    required this.notation,
  });

  @override
  String toString() {
    String rollsStr = rolls.join(', ');
    if (modifier != 0) {
      return '$notation: [$rollsStr] + $modifier = $total';
    } else {
      return '$notation: [$rollsStr] = $total';
    }
  }
}

/// 属性掷骰结果
class AttributeRollResult {
  final DiceRollResult diceResult;
  final int attributeValue;
  final int modifier;

  const AttributeRollResult({
    required this.diceResult,
    required this.attributeValue,
    required this.modifier,
  });

  @override
  String toString() {
    String modifierStr = modifier >= 0 ? '+$modifier' : '$modifier';
    return '${diceResult.notation}: ${diceResult.rolls.join('+')} = $attributeValue ($modifierStr)';
  }
}

/// 生命值掷骰结果
class HpRollResult {
  final int baseHp;
  final DiceRollResult bonusRoll;
  final int totalHp;

  const HpRollResult({
    required this.baseHp,
    required this.bonusRoll,
    required this.totalHp,
  });

  @override
  String toString() {
    return 'HP: $baseHp + ${bonusRoll.total} = $totalHp';
  }
}
