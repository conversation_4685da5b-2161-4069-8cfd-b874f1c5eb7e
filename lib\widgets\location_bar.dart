import 'package:flutter/material.dart';

/// 地点条组件
/// 显示当前所在地点的信息
class LocationBar extends StatelessWidget {
  final String icon;
  final String locationName;
  final VoidCallback? onBackPressed;

  const LocationBar({
    super.key,
    required this.icon,
    required this.locationName,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 返回按钮（如果有）
          if (onBackPressed != null) ...[
            IconButton(
              onPressed: onBackPressed,
              icon: const Icon(Icons.arrow_back),
              tooltip: '返回',
            ),
            const SizedBox(width: 8),
          ],
          
          // 地点图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            child: Center(
              child: Text(
                icon,
                style: const TextStyle(fontSize: 20),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 地点名称
          Expanded(
            child: Text(
              locationName,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
          
          // 右侧操作区域（可扩展）
          // TODO: 可以添加设置按钮、通知按钮等
        ],
      ),
    );
  }
}
