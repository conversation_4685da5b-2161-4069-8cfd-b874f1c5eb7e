// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'D&D游戏';

  @override
  String get characterSelection => '角色选择';

  @override
  String get characterCreation => '创建角色';

  @override
  String get createCharacter => '创建角色';

  @override
  String get enterGame => '进入游戏';

  @override
  String get delete => '删除';

  @override
  String get noCharacters => '还没有角色';

  @override
  String get noCharactersHint => '点击下方\"创建角色\"按钮开始创建你的第一个角色';

  @override
  String get basicInfo => '1. 基本信息';

  @override
  String get raceSelection => '2. 种族选择';

  @override
  String get classSelection => '3. 职业选择';

  @override
  String get currentCurrency => '4. 当前货币';

  @override
  String get attributesAndHp => '5. 属性与生命值掷骰';

  @override
  String get characterName => '角色姓名';

  @override
  String get gender => '性别：';

  @override
  String get male => '男';

  @override
  String get female => '女';

  @override
  String get other => '其他';

  @override
  String get age => '年龄';

  @override
  String get raceHuman => '人类';

  @override
  String get raceElf => '精灵';

  @override
  String get raceDwarf => '矮人';

  @override
  String get raceHalfling => '半身人';

  @override
  String get classFighter => '战士';

  @override
  String get classRogue => '盗贼';

  @override
  String get classMage => '法师';

  @override
  String get classCleric => '牧师';

  @override
  String get hitPoints => '生命值';

  @override
  String get strength => '力量';

  @override
  String get dexterity => '敏捷';

  @override
  String get mind => '心智';

  @override
  String get rerollAttributes => '重新掷骰所有属性和生命值';

  @override
  String get back => '返回';

  @override
  String get confirm => '确认';

  @override
  String get cancel => '取消';

  @override
  String get confirmDelete => '确认删除';

  @override
  String deleteCharacterConfirm(String name) {
    return '确定要删除角色\"$name\"吗？此操作无法撤销。';
  }

  @override
  String get confirmCreateCharacter => '确认创建角色';

  @override
  String get backToModify => '返回修改';

  @override
  String get confirmCreate => '确认创建';

  @override
  String get level => '等级';

  @override
  String get yearsOld => '岁';

  @override
  String startingGold(int amount, String className) {
    return '起始金币：${amount}gp ($className)';
  }

  @override
  String selectedRace(String raceName) {
    return '选中种族：$raceName';
  }

  @override
  String selectedClass(String className) {
    return '选中职业：$className';
  }

  @override
  String get attributeBonus => '属性加成';

  @override
  String get racialAdvantage => '种族优势';

  @override
  String get recommendedClasses => '推荐职业：';

  @override
  String get armorRestriction => '护甲限制';

  @override
  String get specialSkill => '专精技能';

  @override
  String get classCharacteristics => '职业特点';

  @override
  String get skillDescription => '技能说明：';

  @override
  String get pleaseEnterName => '请输入角色姓名';

  @override
  String get nameTooLong => '角色姓名不能超过20个字符';

  @override
  String get pleaseEnterAge => '请输入年龄';

  @override
  String get invalidAge => '请输入有效年龄(1-1000)';

  @override
  String get pleaseSelectRace => '请选择种族';

  @override
  String get pleaseSelectClass => '请选择职业';

  @override
  String get attributeDataError => '属性数据异常，请重新掷骰';
}
