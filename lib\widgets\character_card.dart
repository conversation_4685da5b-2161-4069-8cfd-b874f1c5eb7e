import 'package:flutter/material.dart';
import '../models/character.dart';

/// 角色信息卡片组件
/// 显示角色的基本信息，支持选择和删除操作
class CharacterCard extends StatelessWidget {
  final Character character;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const CharacterCard({
    super.key,
    required this.character,
    this.isSelected = false,
    this.onTap,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 8 : 2,
      color: isSelected 
          ? Theme.of(context).colorScheme.primaryContainer
          : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部信息
              Row(
                children: [
                  // 角色头像和基本信息
                  Expanded(
                    child: Row(
                      children: [
                        // 头像区域
                        _buildAvatar(),
                        const SizedBox(width: 12),
                        // 基本信息
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 角色名称
                              Text(
                                character.name,
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: isSelected 
                                      ? Theme.of(context).colorScheme.onPrimaryContainer
                                      : null,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              // 种族和职业
                              Text(
                                '${character.race.displayName} ${character.characterClass.displayName}',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: isSelected 
                                      ? Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8)
                                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                              const SizedBox(height: 2),
                              // 等级和经验
                              Text(
                                '等级 ${character.level} • ${character.gender} • ${character.age}岁',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: isSelected 
                                      ? Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.6)
                                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 选中状态指示器
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                  // 删除按钮
                  if (onDelete != null)
                    IconButton(
                      onPressed: onDelete,
                      icon: const Icon(Icons.delete_outline),
                      color: Colors.red.withOpacity(0.7),
                      tooltip: '删除角色',
                    ),
                ],
              ),
              const SizedBox(height: 12),
              // 状态区域
              _buildStatusBar(),
              const SizedBox(height: 12),
              // 属性信息
              _buildAttributesRow(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: _getClassColor().withOpacity(0.2),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: _getClassColor(),
          width: 2,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              character.race.icon,
              style: const TextStyle(fontSize: 20),
            ),
            Text(
              character.characterClass.icon,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建状态栏
  Widget _buildStatusBar() {
    final maxHp = character.maxHitPoints;
    final currentHp = maxHp; // 暂时假设满血
    final hpPercentage = currentHp / maxHp;

    return Column(
      children: [
        // 生命值
        Row(
          children: [
            const Icon(Icons.favorite, color: Colors.red, size: 16),
            const SizedBox(width: 4),
            Expanded(
              child: LinearProgressIndicator(
                value: hpPercentage,
                backgroundColor: Colors.red.withOpacity(0.2),
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '$currentHp/$maxHp',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 金币
        Row(
          children: [
            const Icon(Icons.monetization_on, color: Colors.amber, size: 16),
            const SizedBox(width: 4),
            Text(
              '${(character.currency / 100).floor()}gp',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.amber,
              ),
            ),
            const Spacer(),
            // 护甲等级
            const Icon(Icons.shield, color: Colors.blue, size: 16),
            const SizedBox(width: 4),
            Text(
              'AC ${character.armorClass}',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建属性行
  Widget _buildAttributesRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildAttributeItem('STR', character.attributes.strength, character.strengthModifier),
        _buildAttributeItem('DEX', character.attributes.dexterity, character.dexterityModifier),
        _buildAttributeItem('MIND', character.attributes.mind, character.mindModifier),
      ],
    );
  }

  /// 构建单个属性项
  Widget _buildAttributeItem(String label, int value, int modifier) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value.toString(),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          modifier >= 0 ? '+$modifier' : '$modifier',
          style: TextStyle(
            fontSize: 10,
            color: modifier >= 0 ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// 获取职业颜色
  Color _getClassColor() {
    switch (character.characterClass.name) {
      case 'Fighter':
        return Colors.brown;
      case 'Rogue':
        return Colors.grey;
      case 'Mage':
        return Colors.blue;
      case 'Cleric':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }
}
