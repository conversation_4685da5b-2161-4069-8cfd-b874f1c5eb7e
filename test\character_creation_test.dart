import 'package:flutter_test/flutter_test.dart';
import 'package:dnd_game/models/character.dart';
import 'package:dnd_game/models/attributes.dart';
import 'package:dnd_game/models/race.dart';
import 'package:dnd_game/models/character_class.dart';
import 'package:dnd_game/services/character_service.dart';
import 'package:dnd_game/services/dice_service.dart';

void main() {
  group('Character Creation Tests', () {
    test('Create a basic character', () {
      // 创建测试属性
      final attributes = Attributes(
        strength: 15,
        dexterity: 13,
        mind: 12,
        hpBonus: 4,
      );

      // 获取种族和职业
      final race = Race.getRaceByName('Human')!;
      final characterClass = CharacterClass.getClassByName('Fighter')!;

      // 创建角色
      final character = Character(
        name: '测试角色',
        gender: '男',
        age: 25,
        race: race,
        characterClass: characterClass,
        attributes: attributes,
      );

      // 验证角色属性
      expect(character.name, '测试角色');
      expect(character.gender, '男');
      expect(character.age, 25);
      expect(character.race.name, 'Human');
      expect(character.characterClass.name, 'Fighter');
      expect(character.level, 1);
      expect(character.experience, 0);
    });

    test('Test racial bonuses', () {
      final baseAttributes = Attributes(
        strength: 10,
        dexterity: 10,
        mind: 10,
      );

      // 测试精灵种族加成
      final elfRace = Race.getRaceByName('Elf')!;
      baseAttributes.applyRacialBonus(elfRace.name);
      expect(baseAttributes.mind, 12); // +2 MIND

      // 重置并测试矮人种族加成
      baseAttributes.removeRacialBonus(elfRace.name);
      final dwarfRace = Race.getRaceByName('Dwarf')!;
      baseAttributes.applyRacialBonus(dwarfRace.name);
      expect(baseAttributes.strength, 12); // +2 STR

      // 重置并测试半身人种族加成
      baseAttributes.removeRacialBonus(dwarfRace.name);
      final halflingRace = Race.getRaceByName('Halfling')!;
      baseAttributes.applyRacialBonus(halflingRace.name);
      expect(baseAttributes.dexterity, 12); // +2 DEX
    });

    test('Test attribute modifiers', () {
      final attributes = Attributes(
        strength: 15,  // +2 modifier
        dexterity: 8,  // -1 modifier
        mind: 13,      // +1 modifier
      );

      expect(attributes.strengthModifier, 2);
      expect(attributes.dexterityModifier, -1);
      expect(attributes.mindModifier, 1);
    });

    test('Test character stats calculation', () {
      final attributes = Attributes(
        strength: 16,
        dexterity: 14,
        mind: 12,
        hpBonus: 5,
      );

      final race = Race.getRaceByName('Human')!;
      final characterClass = CharacterClass.getClassByName('Fighter')!;

      final character = Character(
        name: '测试战士',
        gender: '女',
        age: 30,
        race: race,
        characterClass: characterClass,
        attributes: attributes,
      );

      // 测试生命值计算 (STR + HP奖励)
      expect(character.maxHitPoints, 21); // 16 + 5

      // 测试护甲等级计算 (10 + DEX加成)
      expect(character.armorClass, 12); // 10 + 2

      // 测试攻击加成 (STR加成 + 等级)
      expect(character.attackBonus, 4); // 3 + 1
    });

    test('Test dice rolling', () {
      // 测试基本掷骰
      for (int i = 0; i < 100; i++) {
        final result = DiceService.rollDie(6);
        expect(result, greaterThanOrEqualTo(1));
        expect(result, lessThanOrEqualTo(6));
      }

      // 测试属性掷骰
      for (int i = 0; i < 100; i++) {
        final result = DiceService.rollAttribute();
        expect(result, greaterThanOrEqualTo(3));
        expect(result, lessThanOrEqualTo(18));
      }

      // 测试多骰子掷骰
      final result = DiceService.rollDice(3, 6);
      expect(result, greaterThanOrEqualTo(3));
      expect(result, lessThanOrEqualTo(18));
    });

    test('Test character JSON serialization', () {
      final attributes = Attributes(
        strength: 15,
        dexterity: 13,
        mind: 12,
        hpBonus: 4,
      );

      final race = Race.getRaceByName('Elf')!;
      final characterClass = CharacterClass.getClassByName('Mage')!;

      final character = Character(
        name: '精灵法师',
        gender: '女',
        age: 120,
        race: race,
        characterClass: characterClass,
        attributes: attributes,
      );

      // 转换为JSON
      final json = character.toJson();
      expect(json['name'], '精灵法师');
      expect(json['race']['name'], 'Elf');
      expect(json['characterClass']['name'], 'Mage');

      // 从JSON恢复
      final restoredCharacter = Character.fromJson(json);
      expect(restoredCharacter.name, character.name);
      expect(restoredCharacter.race.name, character.race.name);
      expect(restoredCharacter.characterClass.name, character.characterClass.name);
      expect(restoredCharacter.attributes.strength, character.attributes.strength);
    });

    test('Test character validation', () {
      final validAttributes = Attributes(
        strength: 15,
        dexterity: 13,
        mind: 12,
        hpBonus: 4,
      );

      final race = Race.getRaceByName('Human')!;
      final characterClass = CharacterClass.getClassByName('Fighter')!;

      // 有效角色
      final validCharacter = Character(
        name: '有效角色',
        gender: '男',
        age: 25,
        race: race,
        characterClass: characterClass,
        attributes: validAttributes,
      );

      expect(CharacterService.validateCharacter(validCharacter), true);

      // 无效角色 - 空名称
      final invalidCharacter = Character(
        name: '',
        gender: '男',
        age: 25,
        race: race,
        characterClass: characterClass,
        attributes: validAttributes,
      );

      expect(CharacterService.validateCharacter(invalidCharacter), false);

      // 无效角色 - 年龄超出范围
      final invalidAgeCharacter = Character(
        name: '无效年龄',
        gender: '男',
        age: 2000,
        race: race,
        characterClass: characterClass,
        attributes: validAttributes,
      );

      expect(CharacterService.validateCharacter(invalidAgeCharacter), false);
    });
  });
}
