{"@@locale": "zh", "appTitle": "D&D游戏", "@appTitle": {"description": "应用标题"}, "characterSelection": "角色选择", "@characterSelection": {"description": "角色选择界面标题"}, "characterCreation": "创建角色", "@characterCreation": {"description": "角色创建界面标题"}, "createCharacter": "创建角色", "@createCharacter": {"description": "创建角色按钮"}, "enterGame": "进入游戏", "@enterGame": {"description": "进入游戏按钮"}, "delete": "删除", "@delete": {"description": "删除按钮"}, "noCharacters": "还没有角色", "@noCharacters": {"description": "没有角色时的提示"}, "noCharactersHint": "点击下方\"创建角色\"按钮开始创建你的第一个角色", "@noCharactersHint": {"description": "没有角色时的提示文本"}, "basicInfo": "1. 基本信息", "@basicInfo": {"description": "基本信息标题"}, "raceSelection": "2. 种族选择", "@raceSelection": {"description": "种族选择标题"}, "classSelection": "3. 职业选择", "@classSelection": {"description": "职业选择标题"}, "currentCurrency": "4. 当前货币", "@currentCurrency": {"description": "当前货币标题"}, "attributesAndHp": "5. 属性与生命值掷骰", "@attributesAndHp": {"description": "属性与生命值标题"}, "characterName": "角色姓名", "@characterName": {"description": "角色姓名输入框标签"}, "gender": "性别：", "@gender": {"description": "性别标签"}, "male": "男", "@male": {"description": "男性"}, "female": "女", "@female": {"description": "女性"}, "other": "其他", "@other": {"description": "其他性别"}, "age": "年龄", "@age": {"description": "年龄输入框标签"}, "raceHuman": "人类", "@raceHuman": {"description": "人类种族"}, "raceElf": "精灵", "@raceElf": {"description": "精灵种族"}, "raceDwarf": "矮人", "@raceDwarf": {"description": "矮人种族"}, "raceHalfling": "半身人", "@raceHalfling": {"description": "半身人种族"}, "classFighter": "战士", "@classFighter": {"description": "战士职业"}, "classRogue": "盗贼", "@classRogue": {"description": "盗贼职业"}, "classMage": "法师", "@classMage": {"description": "法师职业"}, "classCleric": "牧师", "@classCleric": {"description": "牧师职业"}, "hitPoints": "生命值", "@hitPoints": {"description": "生命值"}, "strength": "力量", "@strength": {"description": "力量属性"}, "dexterity": "敏捷", "@dexterity": {"description": "敏捷属性"}, "mind": "心智", "@mind": {"description": "心智属性"}, "rerollAttributes": "重新掷骰所有属性和生命值", "@rerollAttributes": {"description": "重新掷骰按钮"}, "back": "返回", "@back": {"description": "返回按钮"}, "confirm": "确认", "@confirm": {"description": "确认按钮"}, "cancel": "取消", "@cancel": {"description": "取消按钮"}, "confirmDelete": "确认删除", "@confirmDelete": {"description": "确认删除对话框标题"}, "deleteCharacterConfirm": "确定要删除角色\"{name}\"吗？此操作无法撤销。", "@deleteCharacterConfirm": {"description": "删除角色确认文本", "placeholders": {"name": {"type": "String", "description": "角色名称"}}}, "confirmCreateCharacter": "确认创建角色", "@confirmCreateCharacter": {"description": "确认创建角色对话框标题"}, "backToModify": "返回修改", "@backToModify": {"description": "返回修改按钮"}, "confirmCreate": "确认创建", "@confirmCreate": {"description": "确认创建按钮"}, "level": "等级", "@level": {"description": "等级"}, "yearsOld": "岁", "@yearsOld": {"description": "岁数单位"}, "startingGold": "起始金币：{amount}gp ({className})", "@startingGold": {"description": "起始金币文本", "placeholders": {"amount": {"type": "int", "description": "金币数量"}, "className": {"type": "String", "description": "职业名称"}}}, "selectedRace": "选中种族：{raceName}", "@selectedRace": {"description": "选中种族文本", "placeholders": {"raceName": {"type": "String", "description": "种族名称"}}}, "selectedClass": "选中职业：{className}", "@selectedClass": {"description": "选中职业文本", "placeholders": {"className": {"type": "String", "description": "职业名称"}}}, "attributeBonus": "属性加成", "@attributeBonus": {"description": "属性加成标签"}, "racialAdvantage": "种族优势", "@racialAdvantage": {"description": "种族优势标签"}, "recommendedClasses": "推荐职业：", "@recommendedClasses": {"description": "推荐职业标签"}, "armorRestriction": "护甲限制", "@armorRestriction": {"description": "护甲限制标签"}, "specialSkill": "专精技能", "@specialSkill": {"description": "专精技能标签"}, "classCharacteristics": "职业特点", "@classCharacteristics": {"description": "职业特点标签"}, "skillDescription": "技能说明：", "@skillDescription": {"description": "技能说明标签"}, "pleaseEnterName": "请输入角色姓名", "@pleaseEnterName": {"description": "请输入角色姓名错误提示"}, "nameTooLong": "角色姓名不能超过20个字符", "@nameTooLong": {"description": "角色姓名过长错误提示"}, "pleaseEnterAge": "请输入年龄", "@pleaseEnterAge": {"description": "请输入年龄错误提示"}, "invalidAge": "请输入有效年龄(1-1000)", "@invalidAge": {"description": "无效年龄错误提示"}, "pleaseSelectRace": "请选择种族", "@pleaseSelectRace": {"description": "请选择种族错误提示"}, "pleaseSelectClass": "请选择职业", "@pleaseSelectClass": {"description": "请选择职业错误提示"}, "attributeDataError": "属性数据异常，请重新掷骰", "@attributeDataError": {"description": "属性数据异常错误提示"}}