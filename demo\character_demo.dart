import '../lib/models/character.dart';
import '../lib/models/attributes.dart';
import '../lib/models/race.dart';
import '../lib/models/character_class.dart';
import '../lib/services/character_service.dart';
import '../lib/services/dice_service.dart';

/// 角色选择系统演示
/// 展示如何创建和管理角色
void main() {
  print('🎲 D&D Game - 角色选择系统演示');
  print('=' * 50);
  
  // 演示1: 创建不同类型的角色
  demonstrateCharacterCreation();
  
  print('\n' + '=' * 50);
  
  // 演示2: 展示掷骰子系统
  demonstrateDiceRolling();
  
  print('\n' + '=' * 50);
  
  // 演示3: 展示种族和职业特性
  demonstrateRacialAndClassFeatures();
  
  print('\n' + '=' * 50);
  
  // 演示4: 展示角色数据序列化
  demonstrateCharacterSerialization();
}

/// 演示角色创建
void demonstrateCharacterCreation() {
  print('\n📝 演示1: 角色创建');
  print('-' * 30);
  
  // 创建不同种族和职业的角色
  final characters = <Character>[];
  
  // 人类战士
  characters.add(_createSampleCharacter(
    '艾登',
    '男',
    25,
    'Human',
    'Fighter',
  ));
  
  // 精灵法师
  characters.add(_createSampleCharacter(
    '艾莉娜',
    '女',
    120,
    'Elf',
    'Mage',
  ));
  
  // 矮人战士
  characters.add(_createSampleCharacter(
    '索林',
    '男',
    80,
    'Dwarf',
    'Fighter',
  ));
  
  // 半身人盗贼
  characters.add(_createSampleCharacter(
    '比尔博',
    '男',
    35,
    'Halfling',
    'Rogue',
  ));
  
  // 显示所有角色信息
  for (final character in characters) {
    _displayCharacterInfo(character);
    print('');
  }
}

/// 演示掷骰子系统
void demonstrateDiceRolling() {
  print('\n🎲 演示2: 掷骰子系统');
  print('-' * 30);
  
  // 演示基本掷骰
  print('基本掷骰演示:');
  for (int i = 1; i <= 6; i++) {
    final result = DiceService.rollDie(i);
    print('  1d$i: $result');
  }
  
  print('\n属性掷骰演示 (3d6):');
  for (int i = 0; i < 5; i++) {
    final result = DiceService.rollAttributeWithDetails();
    print('  ${result.toString()}');
  }
  
  print('\n生命值掷骰演示:');
  for (int i = 0; i < 3; i++) {
    final baseHp = 15;
    final result = DiceService.rollHitPoints(baseHp);
    print('  ${result.toString()}');
  }
  
  print('\n骰子表达式解析演示:');
  final expressions = ['2d6+3', '1d20', '4d4+1', '3d8-2'];
  for (final expr in expressions) {
    final result = DiceService.parseAndRoll(expr);
    if (result != null) {
      print('  $expr: ${result.toString()}');
    }
  }
}

/// 演示种族和职业特性
void demonstrateRacialAndClassFeatures() {
  print('\n🧝 演示3: 种族和职业特性');
  print('-' * 30);
  
  print('种族特性:');
  for (final race in Race.allRaces) {
    print('  ${race.icon} ${race.displayName}:');
    print('    属性加成: ${race.attributeBonus}');
    print('    种族优势: ${race.advantage}');
    print('    推荐职业: ${race.recommendedClasses.map((c) => _getClassDisplayName(c)).join(', ')}');
    print('');
  }
  
  print('职业特性:');
  for (final characterClass in CharacterClass.allClasses) {
    print('  ${characterClass.icon} ${characterClass.displayName}:');
    print('    护甲限制: ${characterClass.armorRestriction}');
    print('    专精技能: ${characterClass.specialSkill} +3');
    print('    起始金币: ${characterClass.startingGold}gp');
    print('    职业特点: ${characterClass.characteristics}');
    print('');
  }
}

/// 演示角色数据序列化
void demonstrateCharacterSerialization() {
  print('\n💾 演示4: 角色数据序列化');
  print('-' * 30);
  
  // 创建一个测试角色
  final character = _createSampleCharacter(
    '测试角色',
    '女',
    28,
    'Elf',
    'Cleric',
  );
  
  print('原始角色:');
  _displayCharacterInfo(character);
  
  // 序列化为JSON
  final json = character.toJson();
  print('\nJSON序列化:');
  print('  角色ID: ${json['id']}');
  print('  基本信息: ${json['name']}, ${json['gender']}, ${json['age']}岁');
  print('  种族: ${json['race']['displayName']}');
  print('  职业: ${json['characterClass']['displayName']}');
  print('  属性: STR=${json['attributes']['strength']}, DEX=${json['attributes']['dexterity']}, MIND=${json['attributes']['mind']}');
  
  // 从JSON反序列化
  final restoredCharacter = Character.fromJson(json);
  print('\n从JSON恢复的角色:');
  _displayCharacterInfo(restoredCharacter);
  
  // 验证数据一致性
  final isEqual = character.id == restoredCharacter.id &&
                  character.name == restoredCharacter.name &&
                  character.race.name == restoredCharacter.race.name &&
                  character.characterClass.name == restoredCharacter.characterClass.name;
  
  print('\n数据一致性检查: ${isEqual ? '✅ 通过' : '❌ 失败'}');
}

/// 创建示例角色
Character _createSampleCharacter(
  String name,
  String gender,
  int age,
  String raceName,
  String className,
) {
  final race = Race.getRaceByName(raceName)!;
  final characterClass = CharacterClass.getClassByName(className)!;
  
  return CharacterService.createCharacter(
    name: name,
    gender: gender,
    age: age,
    race: race,
    characterClass: characterClass,
  );
}

/// 显示角色信息
void _displayCharacterInfo(Character character) {
  print('${character.race.icon}${character.characterClass.icon} ${character.name}');
  print('  ${character.race.displayName} ${character.characterClass.displayName}, ${character.gender}, ${character.age}岁');
  print('  属性: STR=${character.attributes.strength}(${_formatModifier(character.strengthModifier)}), '
        'DEX=${character.attributes.dexterity}(${_formatModifier(character.dexterityModifier)}), '
        'MIND=${character.attributes.mind}(${_formatModifier(character.mindModifier)})');
  print('  生命值: ${character.maxHitPoints}, 护甲等级: ${character.armorClass}, 攻击加成: ${_formatModifier(character.attackBonus)}');
  print('  金币: ${(character.currency / 100).floor()}gp');
  
  // 显示技能等级
  final skills = ['Physical', 'Subterfuge', 'Knowledge', 'Communication'];
  final skillLevels = skills.map((skill) => '$skill: ${character.getSkillLevel(skill)}').join(', ');
  print('  技能: $skillLevels');
}

/// 格式化属性加成
String _formatModifier(int modifier) {
  return modifier >= 0 ? '+$modifier' : '$modifier';
}

/// 获取职业显示名称
String _getClassDisplayName(String className) {
  switch (className) {
    case 'Fighter':
      return '战士';
    case 'Rogue':
      return '盗贼';
    case 'Mage':
      return '法师';
    case 'Cleric':
      return '牧师';
    default:
      return className;
  }
}
