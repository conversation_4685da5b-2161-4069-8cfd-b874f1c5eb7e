import 'dart:developer' as developer;
import 'package:logger/logger.dart';

/// 统一的日志服务
/// 提供文件日志和控制台日志功能
class LoggerService {
  static Logger? _logger;
  static bool _isInitialized = false;

  /// 初始化日志服务
  static void initialize({bool enableFileLogging = true}) {
    if (_isInitialized) return;

    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      output: enableFileLogging ? MultiOutput([
        ConsoleOutput(),
        // TODO: 添加文件输出
      ]) : ConsoleOutput(),
    );

    _isInitialized = true;
    info('LoggerService', 'Logger initialized');
  }

  /// 确保日志服务已初始化
  static void _ensureInitialized() {
    if (!_isInitialized) {
      initialize();
    }
  }

  /// 记录调试信息
  static void debug(String tag, String message, [dynamic error, StackTrace? stackTrace]) {
    _ensureInitialized();
    _logger!.d('[$tag] $message', error: error, stackTrace: stackTrace);
    developer.log(message, name: tag, level: 500);
  }

  /// 记录一般信息
  static void info(String tag, String message, [dynamic error, StackTrace? stackTrace]) {
    _ensureInitialized();
    _logger!.i('[$tag] $message', error: error, stackTrace: stackTrace);
    developer.log(message, name: tag, level: 800);
  }

  /// 记录警告信息
  static void warning(String tag, String message, [dynamic error, StackTrace? stackTrace]) {
    _ensureInitialized();
    _logger!.w('[$tag] $message', error: error, stackTrace: stackTrace);
    developer.log(message, name: tag, level: 900);
  }

  /// 记录错误信息
  static void error(String tag, String message, [dynamic error, StackTrace? stackTrace]) {
    _ensureInitialized();
    _logger!.e('[$tag] $message', error: error, stackTrace: stackTrace);
    developer.log(message, name: tag, level: 1000, error: error, stackTrace: stackTrace);
  }

  /// 记录严重错误
  static void fatal(String tag, String message, [dynamic error, StackTrace? stackTrace]) {
    _ensureInitialized();
    _logger!.f('[$tag] $message', error: error, stackTrace: stackTrace);
    developer.log(message, name: tag, level: 1200, error: error, stackTrace: stackTrace);
  }

  /// 记录界面进入事件
  static void enterScreen(String screenName, [Map<String, dynamic>? params]) {
    String message = 'Entering screen: $screenName';
    if (params != null && params.isNotEmpty) {
      message += ' with params: $params';
    }
    info('Navigation', message);
  }

  /// 记录界面退出事件
  static void exitScreen(String screenName) {
    info('Navigation', 'Exiting screen: $screenName');
  }

  /// 记录用户操作事件
  static void userAction(String screenName, String action, [Map<String, dynamic>? data]) {
    String message = 'User action in $screenName: $action';
    if (data != null && data.isNotEmpty) {
      message += ' with data: $data';
    }
    info('UserAction', message);
  }

  /// 记录API调用
  static void apiCall(String endpoint, String method, [Map<String, dynamic>? params]) {
    String message = 'API call: $method $endpoint';
    if (params != null && params.isNotEmpty) {
      message += ' with params: $params';
    }
    info('API', message);
  }

  /// 记录API响应
  static void apiResponse(String endpoint, int statusCode, [dynamic response]) {
    String message = 'API response: $endpoint - Status: $statusCode';
    if (response != null) {
      message += ' Response: $response';
    }
    info('API', message);
  }

  /// 记录数据操作
  static void dataOperation(String operation, String entity, [Map<String, dynamic>? data]) {
    String message = 'Data operation: $operation $entity';
    if (data != null && data.isNotEmpty) {
      message += ' with data: $data';
    }
    info('Data', message);
  }

  /// 记录游戏事件
  static void gameEvent(String event, [Map<String, dynamic>? data]) {
    String message = 'Game event: $event';
    if (data != null && data.isNotEmpty) {
      message += ' with data: $data';
    }
    info('Game', message);
  }

  /// 记录性能指标
  static void performance(String operation, Duration duration, [Map<String, dynamic>? metrics]) {
    String message = 'Performance: $operation took ${duration.inMilliseconds}ms';
    if (metrics != null && metrics.isNotEmpty) {
      message += ' metrics: $metrics';
    }
    info('Performance', message);
  }
}

/// 日志标签常量
class LogTags {
  static const String app = 'App';
  static const String navigation = 'Navigation';
  static const String userAction = 'UserAction';
  static const String api = 'API';
  static const String data = 'Data';
  static const String game = 'Game';
  static const String character = 'Character';
  static const String dice = 'Dice';
  static const String storage = 'Storage';
  static const String ui = 'UI';
  static const String performance = 'Performance';
  static const String error = 'Error';
}
