import 'package:flutter/material.dart';
import '../models/race.dart';

/// 种族选择组件
/// 显示所有可选种族，支持选择和查看详细信息
class RaceSelector extends StatelessWidget {
  final Race? selectedRace;
  final Function(Race) onRaceSelected;

  const RaceSelector({
    super.key,
    this.selectedRace,
    required this.onRaceSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 种族选择网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 3.0,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: Race.allRaces.length,
          itemBuilder: (context, index) {
            final race = Race.allRaces[index];
            final isSelected = selectedRace?.name == race.name;

            return _buildRaceCard(context, race, isSelected);
          },
        ),
        
        // 选中种族的详细信息
        if (selectedRace != null) ...[
          const SizedBox(height: 16),
          _buildRaceDetails(context, selectedRace!),
        ],
      ],
    );
  }

  /// 构建种族卡片
  Widget _buildRaceCard(BuildContext context, Race race, bool isSelected) {
    return Card(
      elevation: isSelected ? 8 : 2,
      color: isSelected 
          ? Theme.of(context).colorScheme.primaryContainer
          : null,
      child: InkWell(
        onTap: () => onRaceSelected(race),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 种族图标和名称在一行
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    race.icon,
                    style: const TextStyle(fontSize: 20),
                  ),
                  const SizedBox(width: 6),
                  Flexible(
                    child: Text(
                      race.displayName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected
                            ? Theme.of(context).colorScheme.onPrimaryContainer
                            : null,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // 选中指示器
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).colorScheme.primary,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建种族详细信息
  Widget _buildRaceDetails(BuildContext context, Race race) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Text(
                race.icon,
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(width: 8),
              Text(
                '选中种族：${race.displayName}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // 种族描述
          Text(
            race.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          
          // 属性加成
          _buildDetailRow(
            context,
            '属性加成',
            race.attributeBonus,
            Icons.trending_up,
            Colors.green,
          ),
          const SizedBox(height: 4),
          
          // 种族优势
          _buildDetailRow(
            context,
            '种族优势',
            race.advantage,
            Icons.star,
            Colors.amber,
          ),
          const SizedBox(height: 8),
          
          // 推荐职业
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.recommend,
                size: 16,
                color: Colors.blue,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '推荐职业：',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: race.recommendedClasses.map((className) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.blue.withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            _getClassDisplayName(className),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建详细信息行
  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: Theme.of(context).textTheme.bodySmall,
              children: [
                TextSpan(
                  text: '$label：',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                TextSpan(
                  text: value,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 获取职业显示名称
  String _getClassDisplayName(String className) {
    switch (className) {
      case 'Fighter':
        return '战士';
      case 'Rogue':
        return '盗贼';
      case 'Mage':
        return '法师';
      case 'Cleric':
        return '牧师';
      default:
        return className;
    }
  }
}
