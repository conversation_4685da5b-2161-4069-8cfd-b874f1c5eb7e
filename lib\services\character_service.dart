import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/character.dart';
import '../models/attributes.dart';
import '../models/race.dart';
import '../models/character_class.dart';
import '../services/dice_service.dart';
import '../services/logger_service.dart';

/// 角色数据管理服务
/// 负责角色的创建、保存、加载和管理
class CharacterService {
  static const String _charactersKey = 'saved_characters';
  static const String _selectedCharacterKey = 'selected_character_id';

  /// 获取所有保存的角色
  static Future<List<Character>> getAllCharacters() async {
    try {
      LoggerService.dataOperation('load', 'characters');
      
      final prefs = await SharedPreferences.getInstance();
      final charactersJson = prefs.getString(_charactersKey);
      
      if (charactersJson == null) {
        LoggerService.info(LogTags.character, 'No saved characters found');
        return [];
      }

      final List<dynamic> charactersList = json.decode(charactersJson);
      final characters = charactersList
          .map((json) => Character.fromJson(json))
          .toList();

      LoggerService.info(LogTags.character, 'Loaded ${characters.length} characters');
      return characters;
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.character, 'Failed to load characters', e, stackTrace);
      return [];
    }
  }

  /// 保存角色
  static Future<bool> saveCharacter(Character character) async {
    try {
      LoggerService.dataOperation('save', 'character', {'id': character.id, 'name': character.name});
      
      final characters = await getAllCharacters();
      
      // 检查是否已存在，如果存在则更新，否则添加
      final existingIndex = characters.indexWhere((c) => c.id == character.id);
      if (existingIndex != -1) {
        characters[existingIndex] = character;
        LoggerService.info(LogTags.character, 'Updated existing character: ${character.name}');
      } else {
        characters.add(character);
        LoggerService.info(LogTags.character, 'Added new character: ${character.name}');
      }

      final prefs = await SharedPreferences.getInstance();
      final charactersJson = json.encode(characters.map((c) => c.toJson()).toList());
      await prefs.setString(_charactersKey, charactersJson);

      LoggerService.info(LogTags.character, 'Character saved successfully: ${character.name}');
      return true;
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.character, 'Failed to save character: ${character.name}', e, stackTrace);
      return false;
    }
  }

  /// 删除角色
  static Future<bool> deleteCharacter(String characterId) async {
    try {
      LoggerService.dataOperation('delete', 'character', {'id': characterId});
      
      final characters = await getAllCharacters();
      final originalLength = characters.length;
      
      characters.removeWhere((c) => c.id == characterId);
      
      if (characters.length == originalLength) {
        LoggerService.warning(LogTags.character, 'Character not found for deletion: $characterId');
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      final charactersJson = json.encode(characters.map((c) => c.toJson()).toList());
      await prefs.setString(_charactersKey, charactersJson);

      // 如果删除的是当前选中的角色，清除选中状态
      final selectedId = prefs.getString(_selectedCharacterKey);
      if (selectedId == characterId) {
        await prefs.remove(_selectedCharacterKey);
        LoggerService.info(LogTags.character, 'Cleared selected character');
      }

      LoggerService.info(LogTags.character, 'Character deleted successfully: $characterId');
      return true;
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.character, 'Failed to delete character: $characterId', e, stackTrace);
      return false;
    }
  }

  /// 获取角色
  static Future<Character?> getCharacter(String characterId) async {
    try {
      final characters = await getAllCharacters();
      final matchingCharacters = characters.where((c) => c.id == characterId);
      final character = matchingCharacters.isNotEmpty ? matchingCharacters.first : null;
      
      if (character != null) {
        LoggerService.info(LogTags.character, 'Found character: ${character.name}');
      } else {
        LoggerService.warning(LogTags.character, 'Character not found: $characterId');
      }
      
      return character;
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.character, 'Failed to get character: $characterId', e, stackTrace);
      return null;
    }
  }

  /// 设置选中的角色
  static Future<bool> setSelectedCharacter(String characterId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_selectedCharacterKey, characterId);
      LoggerService.info(LogTags.character, 'Selected character: $characterId');
      return true;
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.character, 'Failed to set selected character: $characterId', e, stackTrace);
      return false;
    }
  }

  /// 获取选中的角色
  static Future<Character?> getSelectedCharacter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedId = prefs.getString(_selectedCharacterKey);
      
      if (selectedId == null) {
        LoggerService.info(LogTags.character, 'No character selected');
        return null;
      }

      return await getCharacter(selectedId);
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.character, 'Failed to get selected character', e, stackTrace);
      return null;
    }
  }

  /// 创建新角色
  static Character createCharacter({
    required String name,
    required String gender,
    required int age,
    required Race race,
    required CharacterClass characterClass,
    Attributes? attributes,
  }) {
    LoggerService.gameEvent('character_creation_start', {
      'name': name,
      'race': race.name,
      'class': characterClass.name,
    });

    // 如果没有提供属性，则随机生成
    if (attributes == null) {
      final attributeRolls = DiceService.rollAllAttributes();
      attributes = Attributes(
        strength: attributeRolls['strength']!,
        dexterity: attributeRolls['dexterity']!,
        mind: attributeRolls['mind']!,
        hpBonus: DiceService.rollHpBonus(),
      );
      
      LoggerService.gameEvent('attributes_rolled', {
        'strength': attributes.strength,
        'dexterity': attributes.dexterity,
        'mind': attributes.mind,
        'hpBonus': attributes.hpBonus,
      });
    }

    // 应用种族加成
    attributes.applyRacialBonus(race.name);

    final character = Character(
      name: name,
      gender: gender,
      age: age,
      race: race,
      characterClass: characterClass,
      attributes: attributes,
    );

    LoggerService.gameEvent('character_created', {
      'id': character.id,
      'name': character.name,
      'finalAttributes': {
        'strength': character.attributes.strength,
        'dexterity': character.attributes.dexterity,
        'mind': character.attributes.mind,
      },
      'maxHp': character.maxHitPoints,
      'ac': character.armorClass,
    });

    return character;
  }

  /// 重新掷骰属性
  static Attributes rerollAttributes() {
    LoggerService.gameEvent('attributes_reroll');
    
    final attributeRolls = DiceService.rollAllAttributes();
    final attributes = Attributes(
      strength: attributeRolls['strength']!,
      dexterity: attributeRolls['dexterity']!,
      mind: attributeRolls['mind']!,
      hpBonus: DiceService.rollHpBonus(),
    );

    LoggerService.gameEvent('attributes_rerolled', {
      'strength': attributes.strength,
      'dexterity': attributes.dexterity,
      'mind': attributes.mind,
      'hpBonus': attributes.hpBonus,
    });

    return attributes;
  }

  /// 验证角色数据
  static bool validateCharacter(Character character) {
    try {
      // 检查基本信息
      if (character.name.trim().isEmpty) {
        LoggerService.warning(LogTags.character, 'Character name is empty');
        return false;
      }

      if (character.age < 1 || character.age > 1000) {
        LoggerService.warning(LogTags.character, 'Invalid character age: ${character.age}');
        return false;
      }

      // 检查属性值
      if (!character.attributes.isValid()) {
        LoggerService.warning(LogTags.character, 'Invalid character attributes');
        return false;
      }

      LoggerService.info(LogTags.character, 'Character validation passed: ${character.name}');
      return true;
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.character, 'Character validation failed', e, stackTrace);
      return false;
    }
  }
}
